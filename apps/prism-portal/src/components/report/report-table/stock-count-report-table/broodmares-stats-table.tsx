'use client';

import { useEffect, useMemo } from 'react';
import { type DataTableProps } from '@glasshouse/components';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { isNil, omit, omitBy } from 'lodash';
import { useTranslations } from 'next-intl';

import { renderTextContent } from './stock-count-report-table-columns';

import { useGetStockCountBroodmaresStats } from '@/api';
import { DataTable } from '@/components/core';
import {
	DATE_FORMAT_6,
	DATE_FORMAT_10,
	type ReportStockCountBroodmaresStatsResponse,
	type ReportStockCountBroodmaresStatsRow,
	useHorseReportContext,
} from '@/core';

export const BroodmaresStatsTable = () => {
	const t = useTranslations('labels.report.stockCount.table');
	const { filterStockCount, setEmptyHorseReport } = useHorseReportContext();

	const sanitizedPayload = useMemo(() => {
		const payload = {
			...omit(filterStockCount, 'dataType'),
		};

		return omitBy(payload, isNil);
	}, [filterStockCount]);

	const fromDate = dayjs(filterStockCount?.from, DATE_FORMAT_10);
	const months = Array.from({ length: 12 }, (_, i) =>
		fromDate.clone().add(i, 'month').startOf('month').format(DATE_FORMAT_6)
	);
	const columns: DataTableProps<ReportStockCountBroodmaresStatsRow>['columns'] = useMemo(() => {
		const cols: DataTableProps<ReportStockCountBroodmaresStatsRow>['columns'] = [
			{
				header: t('lastStatus'),
				accessorKey: 'lastStatus',
				Cell: ({ row }) => renderTextContent(row.original.lastStatus, row.original.index === 0 ? 'font-semibold' : ''),
				size: 120,
				mantineTableBodyCellProps: {
					className: 'uppercase',
					style: { borderRight: '1px solid var(--table-border-color)' },
				},
			},
		];

		months.forEach((month, idx) => {
			cols.push({
				header: month,
				accessorKey: month,
				enableSorting: false,
				Cell: ({ row }) => row.original[`col${idx + 1}` as keyof ReportStockCountBroodmaresStatsRow],
				size: 85,
				mantineTableBodyCellProps: ({ row }) => ({
					align: 'right',
					className: clsx(row.original.index === 0 ? 'font-semibold' : ''),
					style: { borderRight: '1px solid var(--table-border-color)' },
				}),
				mantineTableHeadCellProps: {
					align: 'right',
				},
			});
		});

		return cols;
	}, [t, months]);

	const { data, isLoading } = useGetStockCountBroodmaresStats(sanitizedPayload);
	const { dataSource, isEmpty } = useMemo(() => {
		let isEmpty = true;

		const lastStatuses = [
			{ label: t('total'), value: 'total' },
			{ label: t('served'), value: 'served' },
			{ label: t('scan1Positive'), value: 'scan1Positive' },
			{ label: t('scan1Negative'), value: 'scan1Negative' },
			{ label: t('scan2Positive'), value: 'scan2Positive' },
			{ label: t('scan2Negative'), value: 'scan2Negative' },
			{ label: t('scan3Positive'), value: 'scan3Positive' },
			{ label: t('scan3Negative'), value: 'scan3Negative' },
			{ label: t('mareFoaled'), value: 'mareFoaled' },
			{ label: t('terminated'), value: 'terminated' },
			{ label: t('mareNA'), value: 'mareNA' },
		];
		const dataMap =
			data?.responseData?.reduce(
				(rs, item) => {
					rs[item.actualDate] = item;

					return rs;
				},
				{} as Record<string, ReportStockCountBroodmaresStatsResponse>
			) ?? {};

		const dataSource = lastStatuses.map(({ label, value: key }, idx) => {
			const row: Record<string, string | number | undefined> = { index: idx, lastStatus: label };

			months.forEach((month, idx) => {
				const rowKey = `col${idx + 1}`;

				if (dataMap[month]) {
					const value = dataMap[month][key as keyof ReportStockCountBroodmaresStatsResponse];

					row[rowKey] = value;

					if (value) isEmpty = false;
				}
			});

			return row as unknown as ReportStockCountBroodmaresStatsRow;
		});

		return { dataSource, isEmpty };
	}, [data, months, t]);

	useEffect(() => {
		setEmptyHorseReport(isEmpty);
	}, [isEmpty, setEmptyHorseReport]);

	return (
		<DataTable
			columnResizeMode='onChange'
			columns={columns}
			data={dataSource}
			enableBottomToolbar={false}
			enableColumnOrdering={false}
			enableColumnResizing={false}
			enableStickyHeader
			enableTableActionsColumn={false}
			layoutMode='grid'
			mantinePaperProps={{ mx: '24px' }}
			state={{ showSkeletons: isLoading }}
			mantineTableContainerProps={{
				w: '100%',
			}}
			mantineTableHeadCellProps={{
				className: 'bg-gray-0 uppercase',
			}}
		/>
	);
};
