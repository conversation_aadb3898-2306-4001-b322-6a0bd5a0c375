'use client';

import { useState } from 'react';
import { Button, Checkbox, Group, Tabs, Text, Tooltip } from '@mantine/core';
import { DatePickerInput, MonthPickerInput } from '@mantine/dates';
import dayjs from 'dayjs';
import { CalendarDaysIcon } from 'lucide-react';
import { type Session } from 'next-auth';
import { useTranslations } from 'next-intl';

import { ReportHeaderFilterItem } from '../report-header-filter-item';

import { LocationFilterSelect } from '@/components/horse';
import { TrainerFilterSelect } from '@/components/trainer-filter-select';
import {
	DATE_FORMAT_10,
	DATE_TIME_FORMAT,
	getAccountRole,
	MONTH_YEAR,
	StockCountReportDataType,
	type StockCountReportFilter,
	useHorseReportContext,
	useTrainerContext,
} from '@/core';

const defaultStatsFilter = {
	from: dayjs().subtract(11, 'month').startOf('month').format(DATE_FORMAT_10),
	to: dayjs().startOf('month').format(DATE_FORMAT_10),
	includeArchive: false,
};

const defaultStockCountFilter = {
	fromDate: dayjs().subtract(11, 'week').startOf('month').format(DATE_FORMAT_10),
	includeArchive: false,
	locationIds: [],
};

export const ReportStockCountFilter = ({ session }: { session: Session | null }) => {
	const t = useTranslations('labels');
	const c = useTranslations('labels.common');

	const { trainerId } = useTrainerContext();
	const { setFilterStockCount } = useHorseReportContext();

	const role = getAccountRole(session);
	const isStaff = role.is('Staff');

	const getDefaultFilter = (type: StockCountReportDataType) =>
		type === StockCountReportDataType.BroodmaresStats
			? { trainerId: isStaff ? trainerId : undefined, ...defaultStatsFilter }
			: { trainerId: isStaff ? trainerId : undefined, ...defaultStockCountFilter };

	const [show, setShow] = useState(true);
	const [statsFilter, setStatsFilter] = useState<Partial<StockCountReportFilter>>(
		getDefaultFilter(StockCountReportDataType.BroodmaresStats)
	);
	const [stockCountFilter, setStockCountFilter] = useState<Partial<StockCountReportFilter>>(
		getDefaultFilter(StockCountReportDataType.StockCount)
	);
	const [currentFilter, setCurrentFilter] = useState<StockCountReportFilter>({
		...getDefaultFilter(StockCountReportDataType.BroodmaresStats),
		dataType: StockCountReportDataType.BroodmaresStats,
	});
	const { dataType } = currentFilter;
	const [dateError, setDateError] = useState<string | null>(null);
	const hasDateError = dataType === StockCountReportDataType.BroodmaresStats && Boolean(dateError);

	const validateDate = ({ from, to }: { from: string; to: string }) => {
		if (dataType === StockCountReportDataType.StockCount) return;

		const [fromDate, toDate] = [dayjs(from, DATE_FORMAT_10), dayjs(to, DATE_FORMAT_10)];

		if (toDate.diff(fromDate, 'month') > 12) {
			setDateError(t('report.stockCount.filter.rangeDateMustBeLessThan12Months'));
		} else if (toDate.isBefore(fromDate, 'day')) {
			setDateError(t('report.stockCount.filter.toDateShouldNotLessThanFromDate'));
		} else {
			setDateError(null);
		}
	};

	const handleSearch = () => {
		setFilterStockCount(currentFilter);
	};

	const handleChangeDataType = (type: StockCountReportDataType) => {
		const newFilter: StockCountReportFilter =
			type === StockCountReportDataType.BroodmaresStats
				? { dataType: type, ...statsFilter }
				: { dataType: type, ...stockCountFilter };

		setCurrentFilter(newFilter);
		setFilterStockCount(newFilter);
	};

	const handleReset = () => {
		const defaultFilter = getDefaultFilter(dataType);
		const newFilter = { dataType, ...defaultFilter };

		if (dataType === StockCountReportDataType.BroodmaresStats) {
			setStatsFilter(defaultFilter);
		} else {
			setStockCountFilter(defaultFilter);
		}

		setCurrentFilter(newFilter);
		setFilterStockCount(newFilter);
	};

	const setFieldValues = (values: Partial<StockCountReportFilter>) => {
		if (dataType === StockCountReportDataType.BroodmaresStats) {
			setStatsFilter({ ...statsFilter, ...values });
		} else {
			setStockCountFilter({ ...stockCountFilter, ...values });
		}

		setCurrentFilter({ ...currentFilter, ...values });
	};

	const broodmaresStatsFilterItems = [
		{
			content: (
				<MonthPickerInput
					error={hasDateError}
					label={t('report.stockCount.filter.from')}
					leftSection={<CalendarDaysIcon />}
					value={dayjs(statsFilter.from, DATE_FORMAT_10).toDate()}
					valueFormat={MONTH_YEAR}
					classNames={{
						input: 'text-xs !min-h-8',
						monthsListControl: 'text-xs',
						calendarHeaderLevel: 'text-xs',
						label: 'text-xs',
					}}
					onChange={date => {
						const newDate = dayjs.tz(date).startOf('month').format(DATE_FORMAT_10);

						validateDate({ from: newDate, to: currentFilter.to ?? '' });
						setFieldValues({ from: newDate });
					}}
				/>
			),
		},
		{
			content: (
				<Tooltip
					color='red'
					label={dateError}
					opened={hasDateError}
				>
					<MonthPickerInput
						error={hasDateError}
						label={t('report.stockCount.filter.to')}
						leftSection={<CalendarDaysIcon />}
						value={dayjs(statsFilter.to ?? dayjs().startOf('month').format(DATE_FORMAT_10), DATE_FORMAT_10).toDate()}
						valueFormat={MONTH_YEAR}
						classNames={{
							input: 'text-xs !min-h-8',
							monthsListControl: 'text-xs',
							calendarHeaderLevel: 'text-xs',
							label: 'text-xs',
						}}
						onChange={date => {
							const newDate = dayjs.tz(date).startOf('month').format(DATE_FORMAT_10);

							validateDate({ from: currentFilter.from ?? '', to: newDate });
							setFieldValues({ to: newDate });
						}}
					/>
				</Tooltip>
			),
		},
	];
	const stockCountFilterItems = [
		{
			content: (
				<DatePickerInput
					label={t('report.stockCount.filter.from')}
					maxDate={dayjs().startOf('day').subtract(1, 'day').toDate()}
					value={dayjs(currentFilter.fromDate, DATE_FORMAT_10).startOf('day').toDate()}
					valueFormat={DATE_TIME_FORMAT}
					onChange={val => {
						setFieldValues({ fromDate: dayjs(val).startOf('day').format(DATE_FORMAT_10) });
					}}
				/>
			),
		},
		{
			content: (
				<LocationFilterSelect
					floatingInput
					label={t('report.stockCount.filter.location')}
					placeholder={t('report.stockCount.filter.selectLocation')}
					trainerId={currentFilter.trainerId ?? trainerId ?? undefined}
					value={currentFilter.locationIds ?? []}
					onChange={(_, option) => {
						setFieldValues({ locationIds: option?.map(item => item.value) ?? [] });
					}}
				/>
			),
		},
	];

	const filterItems = [
		...(isStaff
			? [
					{
						content: (
							<TrainerFilterSelect
								allowDeselect={false}
								clearable={false}
								isMultipleItems={false}
								label={t('report.stockCount.filter.trainer')}
								value={currentFilter.trainerId?.toString() ?? trainerId?.toString() ?? ''}
								onChange={val => {
									setFieldValues({ trainerId: val ? parseInt(val) : undefined });
								}}
							/>
						),
					},
				]
			: []),
		...(dataType === StockCountReportDataType.BroodmaresStats ? broodmaresStatsFilterItems : stockCountFilterItems),
		{
			content: (
				<Checkbox
					checked={currentFilter.includeArchive}
					className='mt-5'
					label={t('report.stockCount.filter.includeArchivedHorse')}
					onChange={e => {
						setFieldValues({ includeArchive: e.currentTarget.checked });
					}}
				/>
			),
		},
	];

	return (
		<>
			<Tabs
				defaultValue={StockCountReportDataType.BroodmaresStats}
				value={currentFilter.dataType}
				onChange={value => {
					if (value) {
						handleChangeDataType(value as StockCountReportDataType);
					}
				}}
			>
				<Tabs.List>
					<Tabs.Tab
						key={StockCountReportDataType.BroodmaresStats}
						value={StockCountReportDataType.BroodmaresStats}
					>
						{t('report.stockCount.filter.broodmaresStats')}
					</Tabs.Tab>
					<Tabs.Tab
						key={StockCountReportDataType.StockCount}
						value={StockCountReportDataType.StockCount}
					>
						{t('report.stockCount.filter.stockCount')}
					</Tabs.Tab>
				</Tabs.List>
			</Tabs>

			{show ? (
				<>
					<ReportHeaderFilterItem data={filterItems} />
					{Boolean(dataType === StockCountReportDataType.StockCount) && (
						<Text className='text-foreground-secondary text-xs italic'>
							{t('report.stockCount.filter.stockCountNote')}
						</Text>
					)}
				</>
			) : null}

			<Group
				className='w-full'
				justify='space-between'
				ml='auto'
			>
				<Text
					className='text-primary-7 cursor-pointer italic underline'
					onClick={() => {
						setShow(s => !s);
					}}
				>
					{show ? c('hide') : c('show')}
				</Text>
				<Group ml='auto'>
					<Button
						color='black'
						onClick={handleReset}
						variant='default'
					>
						{t('common.reset')}
					</Button>
					<Button
						disabled={hasDateError}
						onClick={handleSearch}
						variant='filled'
					>
						{t('common.search')}
					</Button>
				</Group>
			</Group>
		</>
	);
};
