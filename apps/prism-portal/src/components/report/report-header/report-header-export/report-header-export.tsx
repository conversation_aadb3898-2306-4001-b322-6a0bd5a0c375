'use client';

import { But<PERSON>, Flex, Group, NumberFormatter, rem, Text } from '@mantine/core';
import { type UseMutateFunction } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { saveAs } from 'file-saver';
import { omit } from 'lodash';
import { Download, TriangleAlertIcon } from 'lucide-react';
import { type Session } from 'next-auth';
import { useTranslations } from 'next-intl';
import { usePathname } from 'next/navigation';
import { useCallback, useEffect, useMemo, useState } from 'react';

import { ReportHeaderExportCommunication } from './report-header-export-communication';
import { ReportHeaderExportEmailLog } from './report-header-export-email-log';
import { PrintButtonEmailLog } from './report-header-export-email-log/print-button-email-log';
import { ReportHeaderExportHorse } from './report-header-export-horse';
import { ReportHeaderExportHorseFinance, ReportHeaderSortHorseFinance } from './report-header-export-horse-finance';
import { ReportHeaderExportHorseSpecific } from './report-header-export-horse-specific';
import { ReportHeaderExportHorsesDaily } from './report-header-export-horses-daily';
import { ReportHeaderExportHorsesReview } from './report-header-export-horses-review';
import { ReportHeaderExportPaddock } from './report-header-export-paddock';
import { PrintButtonPaymentExport } from './report-header-export-payment-export/print-button-payment-export';
import { PrintButtonRaceResults } from './report-header-export-race-results/print-button-race-results';
import { ReportHeaderExportStockCount } from './report-header-export-stock-count';
import { ReportHeaderExportTaskNote } from './report-header-export-task-note';

import {
	apiUrl,
	useEmailLogCsvMutation,
	useGetReportPaymentExportCsv,
	useGetReportRaceResultsExportCsv,
	useHorseReportCsvMutation,
	useLocationStatusCsvMutation,
} from '@/api';
import {
	DATE_FORMAT_COMPACT,
	FeatureKey,
	PaddockReportDataType,
	type Response,
	useHorseReportContext,
	usePermissionGranted,
} from '@/core';
import { useCurrencySetting } from '@/core/context/currency-setting.context';

const comingsoonPage = ['location'];

const RightSection = ({ children }: { children: React.ReactNode }) => (
	<Group
		gap={rem(12)}
		justify='flex-end'
		ml='auto'
	>
		{children}
	</Group>
);

export const ButtonMenuExport = ({ session }: { session: Session | null }) => {
	const t = useTranslations('labels');
	const {
		filterHorseReport,
		emptyHorseReport,
		filterHorseEmailLog,
		filterLocationStatus,
		total,
		filterPaddock,
		filterPayment,
		filterRaceResults,
	} = useHorseReportContext();
	const { mutate: callHorseReportCsv, isPending: pendingHorseReportCsv } = useHorseReportCsvMutation();
	const { mutate: callEmailLogCsv, isPending: pendingEmailLogCsv } = useEmailLogCsvMutation();
	const { mutate: callLocationStatusCsv, isPending: pendingLocationStatusCsv } = useLocationStatusCsvMutation();
	const { mutate: raceResultsExportCsv, isPending: pendingRaceResultsExportCsv } = useGetReportRaceResultsExportCsv();
	const { mutate: paymentExportCsv, isPending: pendingPaymentExportCsv } = useGetReportPaymentExportCsv();

	const currencySetting = useCurrencySetting();
	const harnessRacingGranted = usePermissionGranted(FeatureKey.HarnessRacing, ['Read']);

	const pathname = usePathname();
	const lastPathnameSegment = pathname.split('/').pop();
	const isRaceResults = lastPathnameSegment === 'race-results';
	const paymentReport = lastPathnameSegment === 'payment-report';

	const [exportUrl, setExportUrl] = useState<string | null>(null);

	useEffect(() => {
		if (exportUrl) {
			window.location.href = exportUrl;
			setExportUrl(null);
		}
	}, [exportUrl]);

	const handleReportPaymentExport = useCallback(
		(csvData: string) => {
			if (!csvData) return;

			const fileName = `PaymentReport_${dayjs(filterPayment?.toDate).format(DATE_FORMAT_COMPACT)}.csv`;

			const file = new Blob([csvData as unknown as BlobPart], {
				type: 'text/csv',
			});

			saveAs(file, fileName);
		},
		[filterPayment?.toDate]
	);

	const handleExportCsv = useCallback(() => {
		const exportCsv = <T extends object>(
			fetchFunction: UseMutateFunction<Response<string> | undefined, Error, { payload: T }>,
			payload: T
		) => {
			fetchFunction(
				{ payload },
				{
					onSuccess: rs => {
						if (rs?.responseData) {
							if (paymentReport) {
								handleReportPaymentExport(rs.responseData);
							} else {
								const isFullUrl = rs.responseData.startsWith('http');
								const url = isFullUrl ? rs.responseData : `${apiUrl}/public/export/${rs.responseData}`;

								setExportUrl(url);
							}
						}
					},
				}
			);
		};

		switch (lastPathnameSegment) {
			case 'horse-report':
				exportCsv(callHorseReportCsv, filterHorseReport ?? {});

				return;
			case 'email-log':
				exportCsv(callEmailLogCsv, filterHorseEmailLog ?? {});

				return;
			case 'location-status':
				exportCsv(callLocationStatusCsv, filterLocationStatus ?? {});

				return;
			case 'race-results':
				exportCsv(raceResultsExportCsv, omit(filterRaceResults ?? {}, 'pageIndex', 'pageSize', 'withTotal'));

				return;

			case 'payment-report':
				exportCsv(paymentExportCsv, omit(filterPayment ?? {}, 'pageIndex', 'pageSize', 'withTotal'));

				return;
			default:
				return null;
		}
	}, [
		callEmailLogCsv,
		callHorseReportCsv,
		callLocationStatusCsv,
		filterHorseEmailLog,
		filterHorseReport,
		filterLocationStatus,
		filterPayment,
		filterRaceResults,
		handleReportPaymentExport,
		lastPathnameSegment,
		paymentExportCsv,
		paymentReport,
		raceResultsExportCsv,
	]);

	const isPendingExportCsv = useMemo(() => {
		return (
			pendingEmailLogCsv ||
			pendingHorseReportCsv ||
			pendingLocationStatusCsv ||
			pendingRaceResultsExportCsv ||
			pendingPaymentExportCsv
		);
	}, [
		pendingEmailLogCsv,
		pendingHorseReportCsv,
		pendingLocationStatusCsv,
		pendingRaceResultsExportCsv,
		pendingPaymentExportCsv,
	]);

	const BUTTON_EXPORT_CSV = useMemo(
		() => (
			<Button
				className='flex items-center justify-center'
				disabled={emptyHorseReport}
				leftSection={<Download />}
				loading={isPendingExportCsv}
				radius='md'
				type='submit'
				variant='default'
				onClick={() => {
					handleExportCsv();
				}}
			>
				{t('common.exportCSV')}
			</Button>
		),
		[emptyHorseReport, isPendingExportCsv, t, handleExportCsv]
	);

	const HEADER_EXPORT = useMemo(() => {
		switch (lastPathnameSegment) {
			case 'horse-report':
				return (
					<RightSection>
						{BUTTON_EXPORT_CSV}
						<ReportHeaderExportHorse session={session} />
					</RightSection>
				);

			case 'email-log':
				return (
					<>
						<ReportHeaderExportEmailLog />
						<RightSection>
							<PrintButtonEmailLog />
							{BUTTON_EXPORT_CSV}
						</RightSection>
					</>
				);

			case 'location-status': {
				return <RightSection>{BUTTON_EXPORT_CSV}</RightSection>;
			}

			case 'task-note':
				return (
					<RightSection>
						<ReportHeaderExportTaskNote />
					</RightSection>
				);

			case 'horse-finance-report':
				return (
					<>
						<ReportHeaderSortHorseFinance />
						<RightSection>
							<ReportHeaderExportHorseFinance />
						</RightSection>
					</>
				);

			case 'paddock-report': {
				const getTotal = (type: PaddockReportDataType) => {
					switch (type) {
						case PaddockReportDataType.ByHorseType:
							return total.byHorseType?.total ?? 0;
						case PaddockReportDataType.ByOwner:
							return total.byOwner?.total ?? 0;
						case PaddockReportDataType.Broodmares:
							return total.broodmares?.total ?? 0;
						case PaddockReportDataType.BroodmaresWithFoals:
							return total.broodmaresWithFoals?.total ?? 0;
						default:
							return 0;
					}
				};

				return (
					<>
						<Text
							className='text-foreground-secondary font-semibold'
							size='md'
						>
							{t('report.paddock.filter.total')}:{' '}
							{getTotal(filterPaddock?.dataType ?? PaddockReportDataType.ByHorseType)}
						</Text>
						<RightSection>
							<ReportHeaderExportPaddock />
						</RightSection>
					</>
				);
			}

			case 'horses-daily':
				return <ReportHeaderExportHorsesDaily />;

			case 'horses-review':
				return (
					<RightSection>
						<ReportHeaderExportHorsesReview />
					</RightSection>
				);
			case 'horse-specific':
				return (
					<RightSection>
						<ReportHeaderExportHorseSpecific />
					</RightSection>
				);

			case 'communication-report':
				return (
					<RightSection>
						<ReportHeaderExportCommunication />
					</RightSection>
				);

			case 'payment-report':
				return paymentReport && !filterPayment ? null : (
					<>
						<Group>
							<Text>{t('report.payment.table.totalAmount')}: </Text>
							<NumberFormatter
								decimalScale={2}
								decimalSeparator={currencySetting?.decimalChar}
								fixedDecimalScale
								prefix={currencySetting?.currency}
								thousandSeparator={currencySetting?.thousandChar}
								value={total.paymentAmount?.total}
							/>
						</Group>
						<RightSection>
							<PrintButtonPaymentExport />
							{BUTTON_EXPORT_CSV}
						</RightSection>
					</>
				);

			case 'race-results':
				return harnessRacingGranted || (isRaceResults && !filterRaceResults) ? (
					<RightSection>
						<PrintButtonRaceResults />
						{BUTTON_EXPORT_CSV}
					</RightSection>
				) : (
					<RightSection>
						<PrintButtonRaceResults />
						{BUTTON_EXPORT_CSV}
					</RightSection>
				);

			case 'stock-count':
				return (
					<RightSection>
						<ReportHeaderExportStockCount />
					</RightSection>
				);

			default:
				return null;
		}
	}, [
		lastPathnameSegment,
		BUTTON_EXPORT_CSV,
		session,
		paymentReport,
		filterPayment,
		t,
		currencySetting?.decimalChar,
		currencySetting?.currency,
		currencySetting?.thousandChar,
		total.paymentAmount?.total,
		total.byHorseType?.total,
		total.byOwner?.total,
		total.broodmares?.total,
		total.broodmaresWithFoals?.total,
		harnessRacingGranted,
		isRaceResults,
		filterRaceResults,
		filterPaddock?.dataType,
	]);

	return (
		<>
			{paymentReport ? (
				<Group
					pt={16}
					px={24}
					w='100%'
				>
					<TriangleAlertIcon color='red' />
					<Text>{t('report.payment.table.warning')}</Text>
				</Group>
			) : null}
			<Flex
				align='flex-end'
				className={comingsoonPage.includes(lastPathnameSegment ?? '') ? 'hidden' : 'px-6 pt-4'}
				justify='space-between'
			>
				{HEADER_EXPORT}
			</Flex>
		</>
	);
};
