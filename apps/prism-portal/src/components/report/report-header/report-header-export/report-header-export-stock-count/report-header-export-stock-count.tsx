'use client';

import { useState } from 'react';
import { Button } from '@mantine/core';
import dayjs from 'dayjs';
import { saveAs } from 'file-saver';
import { Download, Printer } from 'lucide-react';
import { useTranslations } from 'next-intl';

import { apiUrl, useGetBroodmaresStatsExport, useGetStockCountExport } from '@/api';
import { StockCountReportDataType, useHorseReportContext } from '@/core';

export const ReportHeaderExportStockCount = () => {
	const t = useTranslations('labels');
	const { filterStockCount } = useHorseReportContext();

	const { mutateAsync: exportBroodmaresStats } = useGetBroodmaresStatsExport();
	const { mutateAsync: exportStockCount } = useGetStockCountExport();

	const [loadingCsv, setLoadingCsv] = useState(false);
	const [loadingPrint, setLoadingPrint] = useState(false);

	const payload = { ...filterStockCount };
	const dataType = payload.dataType;

	delete payload.dataType;

	const downloadFile = (fileName?: string | null) => {
		if (!fileName) return;

		window.location.href = `${apiUrl}/public/export/${fileName}`;
	};

	const handleExportCsv = async () => {
		setLoadingCsv(true);

		if (dataType === StockCountReportDataType.BroodmaresStats) {
			const res = await exportBroodmaresStats({ exportType: 'csv', params: payload });

			if (res) {
				const fileName = `Broodmare_stats_report_${dayjs().format('YYYY_MM_DD')}.csv`;
				const file = new Blob([res as unknown as BlobPart], {
					type: 'text/csv',
				});

				saveAs(file, fileName);
			}
		} else {
			const res = await exportStockCount({ exportType: 'csv', payload });
	
			downloadFile(res?.responseData);
		}

		setLoadingCsv(false);
	};

	const handlePrint = async () => {
		setLoadingPrint(true);

		if (dataType === StockCountReportDataType.BroodmaresStats) {
			const res = await exportBroodmaresStats({ exportType: 'pdf', params: payload });

			downloadFile(res?.responseData);
		} else {
			const res = await exportStockCount({ exportType: 'pdf', payload });

			downloadFile(res?.responseData);
		}

		setLoadingPrint(false);
	};

	return (
		<>
			<Button
				className='flex items-center justify-center'
				leftSection={<Printer />}
				loading={loadingPrint}
				onClick={handlePrint}
				radius='md'
				type='submit'
				variant='default'
			>
				{t('common.print')}
			</Button>
			<Button
				className='flex items-center justify-center'
				leftSection={<Download />}
				loading={loadingCsv}
				onClick={handleExportCsv}
				radius='md'
				type='submit'
				variant='default'
			>
				{t('common.exportCSV')}
			</Button>
		</>
	);
};
