import { stringify } from 'qs';

import { fetcher } from '@/api';
import { errorHandler } from '@/api/utils/error-handler';
import {
	type Assignee,
	CommunicationReportLastUpdateType,
	type CommunicationReportPayload,
	type EmailLogDetail,
	type EmailLogPayload,
	type EmailLogResponse,
	Endpoint,
	type ForwardRequestInfo,
	type HorseTaskNoteResp,
	Method,
	PaddockReportDataType,
	type PaddockReportFilterPayload,
	type PaymentFinanceOwnerAccount,
	type RaceResultsData,
	type RaceResultsDataFilter,
	type RaceResultsPayload,
	type ReportCommunicationOwnerResendPayload,
	type ReportCommunicationResponse,
	type ReportHorseFinancePayload,
	type ReportHorsesByOwnerResponse,
	type ReportPaddockBroodmaresResponse,
	type ReportPaddockByHorseTypeResponse,
	type ReportPaddockByOwnerResponse,
	type ReportPaymentData,
	type ReportPaymentOwnerPayload,
	type ReportPaymentPayload,
	type ReportStockCountBroodmaresStatsResponse,
	type ReportStockCountResponse,
	type Response,
	RevalidateTag,
	type SearchStableRes,
	type SearchTrainerRes,
	type StockCountReportPayload,
	type TaskNoteAssigneesParams,
	type TaskNoteBarnList,
	type TaskNoteBarnPayload,
	type TaskNoteFilter,
	type TaskNoteGearList,
	type TaskNoteGearListPayload,
	type TaskNotePayload,
	type TaskNoteSpecificsItem,
	type TaskNoteSpecificsListParams,
} from '@/core';

export const getReportEmailLog = async (payload: EmailLogPayload) => {
	const _payload = stringify(payload, { encodeValuesOnly: true, arrayFormat: 'comma' });
	const url = `${Endpoint.EmailLogReport}?${_payload}`;

	return fetcher<{
		mailLogs: EmailLogResponse[];
		total?: number;
	}>(url);
};

export const resendEmail = async (msgids: string[]) => {
	return fetcher<null>(Endpoint.EmailResend, {
		method: Method.POST,
		body: JSON.stringify({ msgids }),
	});
};

export const reactivateEmail = async ({ emails, trainerId }: { emails: string[]; trainerId: string | null }) => {
	return fetcher<null>(Endpoint.EmailReactivate, {
		method: Method.POST,
		body: JSON.stringify({ emails, trainerId }),
	});
};

export const exportEmailLogCSV = async (payload: EmailLogPayload) => {
	const _payload = stringify(payload, { encodeValuesOnly: true, arrayFormat: 'comma' });
	const url = `${Endpoint.EmailLogExportCsv}?${_payload}`;

	return fetcher<string>(url);
};

export const forwardEmail = async (payload: ForwardRequestInfo) => {
	const url = Endpoint.ForwardEmailLog;

	return fetcher<string>(url, { method: Method.POST, body: JSON.stringify(payload) });
};

export const getReportSearchStable = async () => {
	return fetcher<SearchStableRes[]>(`${Endpoint.SearchStable}?keyword=prism`);
};

export const getReportSearchTrainer = async (stableId: string) => {
	return fetcher<SearchTrainerRes[]>(`${Endpoint.EmailLogSearchTrainer}?keyword=prism&stableId=${stableId}`);
};

export const getReportEmailLogDetail = async (id: string) => {
	const url = `${Endpoint.EmailLogReport}/${id}`;

	return fetcher<EmailLogDetail>(url);
};

export const getTaskNoteAssignees = async (params: TaskNoteAssigneesParams) => {
	const payload = stringify(params, { addQueryPrefix: true, arrayFormat: 'repeat' });
	const url = `${Endpoint.Assignees}${payload}`;

	return fetcher<Assignee[]>(url, {
		next: { tags: [RevalidateTag.Assignees] },
	});
};

export const getTaskNoteGear = async (payload: TaskNoteGearListPayload) => {
	const url = Endpoint.GearList;

	return fetcher<TaskNoteGearList[]>(url, { method: Method.POST, body: JSON.stringify(payload) });
};

export const getTaskNoteBarn = async (payload: TaskNoteBarnPayload) => {
	const url = `${Endpoint.Barn}/list`;

	return fetcher<TaskNoteBarnList[]>(url, { method: Method.POST, body: JSON.stringify(payload) });
};

export const getTaskNoteSpecificsList = async (params: TaskNoteSpecificsListParams) => {
	const cleanedParams = {
		...params,
		categoryIds: params.categoryIds ? [...new Set(params.categoryIds)] : undefined,
	};

	const payload = stringify(cleanedParams, {
		addQueryPrefix: true,
		encodeValuesOnly: true,
		arrayFormat: 'comma',
	});

	const url = `${Endpoint.ReportSpecificsList}${payload}`;

	return fetcher<TaskNoteSpecificsItem[]>(url);
};

export const getReportTaskNote = async (payload: TaskNotePayload) => {
	return fetcher<HorseTaskNoteResp>(Endpoint.ReportTask, {
		method: Method.POST,
		body: JSON.stringify(payload),
		next: { tags: [RevalidateTag.ReportTask] },
	});
};

export const exportReportTaskNoteCSV = async (payload: TaskNoteFilter) => {
	return fetcher<string>(Endpoint.ReportExport, {
		method: Method.POST,
		body: JSON.stringify(payload),
	});
};

export const getReportHorseByOwner = async (payload: ReportHorseFinancePayload) => {
	return fetcher<ReportHorsesByOwnerResponse[]>(Endpoint.ReportHorseByOwner, {
		method: Method.POST,
		body: JSON.stringify(payload),
	});
};

// export const exportHorsesByOwnerCsv = async (payload: ReportHorseFinancePayload) => {
// 	const _payload = stringify(payload, { encodeValuesOnly: true, arrayFormat: 'comma' });
// 	const url = `${Endpoint.ReportHorseByOwner}/export?${_payload}`;
//
// 	return fetcher<string>(url);
// };
//
// export const exportHorsesByOwnerPdf = async (payload: ReportHorseFinancePayload) => {
// 	const _payload = stringify(payload, { encodeValuesOnly: true, arrayFormat: 'comma' });
// 	const url = `${Endpoint.ReportHorseByOwner}/pdf?${_payload}`;
//
// 	return fetcher<string>(url);
// };

export const getReportPaddockByHorseType = async (params: PaddockReportFilterPayload) => {
	const payload = stringify(params, {
		addQueryPrefix: true,
		encodeValuesOnly: true,
		arrayFormat: 'comma',
	});

	return fetcher<ReportPaddockByHorseTypeResponse>(`${Endpoint.ReportPaddock}${payload}`);
};

export const getReportPaddockByOwner = async (payload: PaddockReportFilterPayload) => {
	return fetcher<ReportPaddockByOwnerResponse>(`${Endpoint.ReportPaddock}/by-owner`, {
		method: Method.POST,
		body: JSON.stringify(payload),
	});
};

export const getReportPaddockBroodmares = async (params: PaddockReportFilterPayload) => {
	const payload = stringify(params, {
		addQueryPrefix: true,
		encodeValuesOnly: true,
		arrayFormat: 'comma',
	});

	return fetcher<ReportPaddockBroodmaresResponse>(`${Endpoint.ReportPaddock}/broodmares${payload}`);
};

export const getReportPaddockBroodmaresWithFoals = async (params: PaddockReportFilterPayload) => {
	const payload = stringify(params, {
		addQueryPrefix: true,
		encodeValuesOnly: true,
		arrayFormat: 'comma',
	});

	return fetcher<ReportPaddockBroodmaresResponse>(`${Endpoint.ReportPaddock}/broodmares-with-foals${payload}`);
};

export const getReportRaceResults = async (payload: RaceResultsPayload) => {
	return fetcher<RaceResultsData>(`${Endpoint.ReportRaceResults}/search`, {
		method: Method.POST,
		body: JSON.stringify(payload),
	});
};

export const getReportRaceResultsFilter = async (payload: RaceResultsPayload) => {
	return fetcher<RaceResultsDataFilter>(`${Endpoint.ReportRaceResults}/filter`, {
		method: Method.POST,
		body: JSON.stringify(payload),
	});
};

export const exportPaddockByHorseTypePdf = async (payload: PaddockReportFilterPayload) => {
	const _payload = stringify(payload, { encodeValuesOnly: true, arrayFormat: 'comma' });
	const url = `${Endpoint.ReportPaddock}/pdf?${_payload}`;

	return fetcher<string>(url);
};

export const exportPaddockByHorseTypeCsv = async (payload: PaddockReportFilterPayload) => {
	const _payload = stringify(payload, { encodeValuesOnly: true, arrayFormat: 'comma' });
	const url = `${Endpoint.ReportPaddock}/export?${_payload}`;

	return fetcher<string>(url);
};

export const exportPaddockByOwnerCsv = async (payload: PaddockReportFilterPayload) => {
	const url = `${Endpoint.ReportPaddock}/by-owner/export`;

	return fetcher<string>(url, {
		method: Method.POST,
		body: JSON.stringify(payload),
	});
};

export const exportPaddockByOwnerPdf = async (payload: PaddockReportFilterPayload) => {
	const url = `${Endpoint.ReportPaddock}/by-owner/pdf`;

	return fetcher<string>(url, {
		method: Method.POST,
		body: JSON.stringify(payload),
	});
};

export const exportPaddockBroodmaresPdf = async (
	payload: PaddockReportFilterPayload,
	paddockType: PaddockReportDataType
) => {
	const _payload = stringify(payload, { encodeValuesOnly: true, arrayFormat: 'comma' });
	const baseUrl = paddockType === PaddockReportDataType.BroodmaresWithFoals ? 'broodmares-with-foals' : 'broodmares';
	const url = `${Endpoint.ReportPaddock}/${baseUrl}/pdf?${_payload}`;

	return fetcher<string>(url);
};

export const exportPaddockBroodmaresCsv = async (
	payload: PaddockReportFilterPayload,
	paddockType: PaddockReportDataType
) => {
	const _payload = stringify(payload, { encodeValuesOnly: true, arrayFormat: 'comma' });
	const baseUrl = paddockType === PaddockReportDataType.BroodmaresWithFoals ? 'broodmares-with-foals' : 'broodmares';
	const url = `${Endpoint.ReportPaddock}/${baseUrl}/export?${_payload}`;

	return fetcher<string>(url);
};

export const exportCommunicationPdf = async (
	type: CommunicationReportLastUpdateType,
	payload: CommunicationReportPayload
) => {
	const _payload = stringify(payload, { encodeValuesOnly: true, arrayFormat: 'comma' });
	const endpoint =
		type === CommunicationReportLastUpdateType.Owner
			? Endpoint.ReportCommunicationOwner
			: Endpoint.ReportCommunicationHorse;
	const url = `${endpoint}/pdf?${_payload}`;

	return fetcher<string>(url);
};

export const exportCommunicationCsv = async (
	type: CommunicationReportLastUpdateType,
	payload: CommunicationReportPayload
) => {
	const endpoint =
		type === CommunicationReportLastUpdateType.Owner
			? Endpoint.ReportCommunicationOwner
			: Endpoint.ReportCommunicationHorse;
	const url = `${endpoint}/export`;

	return fetcher<string>(url, {
		method: Method.POST,
		body: JSON.stringify(payload),
	});
};

export const getReportPayment = async (payload: ReportPaymentPayload) => {
	return fetcher<ReportPaymentData>(`${Endpoint.ManualPayment}/report`, {
		method: Method.POST,
		body: JSON.stringify(payload),
	});
};

export const getReportCommunicationByOwner = async (payload: CommunicationReportPayload) => {
	return fetcher<ReportCommunicationResponse[]>(Endpoint.ReportCommunicationOwner, {
		method: Method.POST,
		body: JSON.stringify(payload),
	});
};

export const getReportPaymentOwner = async (payload: ReportPaymentOwnerPayload) => {
	return fetcher<PaymentFinanceOwnerAccount[]>(`${Endpoint.ManualPayment}/owner`, {
		method: Method.POST,
		body: JSON.stringify(payload),
	});
};

export const getReportCommunicationByHorse = async (payload: CommunicationReportPayload) => {
	return fetcher<ReportCommunicationResponse[]>(Endpoint.ReportCommunicationHorse, {
		method: Method.POST,
		body: JSON.stringify(payload),
	});
};

export const reportCommunicationOwnerResendEmail = async (payload: ReportCommunicationOwnerResendPayload) => {
	return fetcher<null>(`${Endpoint.ReportCommunicationOwner}/resend`, {
		method: Method.POST,
		body: JSON.stringify(payload),
	});
};

export const reportCommunicationHorseResendEmail = async (payload: ReportCommunicationOwnerResendPayload) => {
	return fetcher<null>(`${Endpoint.ReportCommunicationHorse}/resend`, {
		method: Method.POST,
		body: JSON.stringify(payload),
	});
};

export const getReportPaymentExportCsv = async (payload: ReportPaymentOwnerPayload): Promise<Response<string>> => {
	const res = await fetch(`${Endpoint.ManualPayment}/export`, {
		method: 'POST',
		body: JSON.stringify(payload),
	});

	const text = await res.text();

	return {
		responseData: text,
		extraData: null,
		successMessage: null,
		warningMessage: null,
		error: null,
	};
};

export const getReportRaceResultsExportCsv = async (payload: RaceResultsPayload) => {
	return fetcher<string>(`${Endpoint.ReportRaceResults}/export`, {
		method: Method.POST,
		body: JSON.stringify(payload),
	});
};

export const getReportStockCountBroodmaresStats = async (params: StockCountReportPayload) => {
	const payload = stringify(params, {
		addQueryPrefix: true,
		encodeValuesOnly: true,
		arrayFormat: 'comma',
	});

	return fetcher<ReportStockCountBroodmaresStatsResponse[]>(
		`${Endpoint.ReportStockCountBroodmaresStats}/report${payload}`
	);
};

export const getBroodmaresStatsExport = async (exportType: string, params: StockCountReportPayload) => {
	const payload = stringify(params, {
		addQueryPrefix: true,
		encodeValuesOnly: true,
		arrayFormat: 'comma',
	});

	return fetcher<string>(
		`${Endpoint.ReportStockCountBroodmaresStats}/${exportType}${payload}`,
		{ method: Method.GET },
		exportType !== 'csv',
		errorHandler,
		exportType === 'csv'
	);
};

export const getReportStockCount = async (payload: StockCountReportPayload) => {
	return fetcher<ReportStockCountResponse>(`${Endpoint.ReportStockCount}/report`, {
		method: Method.POST,
		body: JSON.stringify(payload),
	});
};

export const getStockCountExport = async (exportType: string, payload?: StockCountReportPayload) => {
	return fetcher<string>(`${Endpoint.ReportStockCount}/${exportType}`, {
		method: Method.POST,
		body: JSON.stringify(payload),
	});
};
