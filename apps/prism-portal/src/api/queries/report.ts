import { useMutation, type UseMutationOptions, useQuery } from '@tanstack/react-query';

import { QueryKey } from '../../core/lib/constants/query';
import type { Response } from '../../core/types/common';
import type { EmailLogPayload } from '../../core/types/email-log';
import type { HorseReport, HorseReportExportPdfType } from '../../core/types/horse';
import type { LocationStatusPayload, UpdateHorseNoteReminderPayload } from '../../core/types/location-status';
import { exportOwnersByHorsePdf } from '../fetchers/finances';
import {
	exportHorseReportCSV,
	exportHorseReportPDF,
	exportHorsesByOwnerCsv,
	exportHorsesByOwnerPdf,
	updateTaskComment,
} from '../fetchers/horse';
import { exportReportLocationStatusCSV, updateHorseNoteReminder } from '../fetchers/location-status';
import {
	exportCommunicationCsv,
	exportCommunicationPdf,
	exportEmailLogCSV,
	exportPaddockBroodmaresCsv,
	exportPaddockBroodmaresPdf,
	exportPaddockByHorseTypeCsv,
	exportPaddockByHorseTypePdf,
	exportPaddockByOwnerCsv,
	exportPaddockByOwnerPdf,
	exportReportTaskNoteCSV,
	getBroodmaresStatsExport,
	getReportEmailLogDetail,
	getReportPaymentExportCsv,
	getReportPaymentOwner,
	getReportRaceResultsExportCsv,
	getReportSearchStable,
	getReportSearchTrainer,
	getReportStockCount,
	getReportStockCountBroodmaresStats,
	getStockCountExport,
	getTaskNoteAssignees,
	getTaskNoteBarn,
	getTaskNoteGear,
	getTaskNoteSpecificsList,
	reactivateEmail,
	reportCommunicationHorseResendEmail,
	reportCommunicationOwnerResendEmail,
	resendEmail,
} from '../fetchers/report';

import {
	type CommunicationReportLastUpdateType,
	type CommunicationReportPayload,
	type PaddockReportDataType,
	type PaddockReportFilterPayload,
	type RaceResultsPayload,
	type ReportCommunicationHorseResendPayload,
	type ReportCommunicationOwnerResendPayload,
	type ReportHorseFinancePayload,
	type ReportPaymentOwnerPayload,
	type StockCountReportPayload,
	type TaskNoteAssigneesParams,
	type TaskNoteBarnPayload,
	type TaskNoteFilter,
	type TaskNoteGearListPayload,
	type TaskNoteSpecificsListParams,
	type UpdateTaskCommentParams,
} from '@/core';

export const useHorseReportPdfMutation = (
	options?: Omit<
		UseMutationOptions<
			Response<string> | undefined,
			Error,
			{ payload: HorseReport; exportType?: HorseReportExportPdfType }
		>,
		'mutationFn'
	>
) => {
	return useMutation({
		mutationFn: ({ payload, exportType }) => exportHorseReportPDF(payload, exportType),
		...options,
	});
};

export const useHorseReportCsvMutation = (
	options?: Omit<UseMutationOptions<Response<string> | undefined, Error, { payload: HorseReport }>, 'mutationFn'>
) => {
	return useMutation({
		mutationFn: ({ payload }) => exportHorseReportCSV(payload),
		...options,
	});
};

export const useResendEmailMutation = (
	options?: Omit<UseMutationOptions<Response<null> | undefined, Error, { msgids: string[] }>, 'mutationFn'>
) => {
	return useMutation({
		mutationFn: ({ msgids }) => resendEmail(msgids),
		...options,
	});
};

export const useReactiveEmailMutation = (
	options?: Omit<
		UseMutationOptions<Response<null> | undefined, Error, { emails: string[]; trainerId: string | null }>,
		'mutationFn'
	>
) => {
	return useMutation({
		mutationFn: (variables: { emails: string[]; trainerId: string | null }) => reactivateEmail(variables),
		...options,
	});
};

export const useEmailLogCsvMutation = (
	options?: Omit<UseMutationOptions<Response<string> | undefined, Error, { payload: EmailLogPayload }>, 'mutationFn'>
) => {
	return useMutation({
		mutationFn: ({ payload }) => exportEmailLogCSV(payload),
		...options,
	});
};

export const useGetSearchStable = (enabled = false) => {
	return useQuery({
		queryKey: [QueryKey.SearchStable],
		queryFn: () => getReportSearchStable(),
		refetchOnWindowFocus: false,
		enabled,
	});
};

export const useGetSearchTrainer = (stableId: string) => {
	return useQuery({
		queryKey: [QueryKey.SearchTrainer, stableId],
		queryFn: () => getReportSearchTrainer(stableId),
		refetchOnWindowFocus: false,
		enabled: Boolean(stableId),
	});
};

export const useGetReportEmailLogDetail = (id: string) => {
	return useQuery({
		queryKey: [QueryKey.EmailLogDetail, id],
		queryFn: () => getReportEmailLogDetail(id),
		refetchOnWindowFocus: false,
		enabled: Boolean(id),
	});
};

export const useLocationStatusCsvMutation = (
	options?: Omit<
		UseMutationOptions<Response<string> | undefined, Error, { payload: LocationStatusPayload }>,
		'mutationFn'
	>
) => {
	return useMutation({
		mutationFn: ({ payload }) => exportReportLocationStatusCSV(payload),
		...options,
	});
};

export const useUpdateHorseNoteReminder = () => {
	return useMutation({
		mutationFn: (payload: UpdateHorseNoteReminderPayload) => updateHorseNoteReminder(payload),
	});
};

export const useGetTaskNoteAssignees = (payload: TaskNoteAssigneesParams) => {
	return useQuery({
		queryFn: () => getTaskNoteAssignees(payload),
		queryKey: [QueryKey.Assignees, JSON.stringify(payload)],
		refetchOnWindowFocus: false,
	});
};

export const useGetTaskNoteGear = (payload: TaskNoteGearListPayload) => {
	return useQuery({
		queryFn: () => getTaskNoteGear(payload),
		queryKey: [QueryKey.GearList, JSON.stringify(payload)],
		refetchOnWindowFocus: false,
	});
};

export const useGetTaskNoteBarn = (payload: TaskNoteBarnPayload, enabled = false) => {
	return useQuery({
		queryFn: () => getTaskNoteBarn(payload),
		queryKey: [QueryKey.BarnList, JSON.stringify(payload)],
		refetchOnWindowFocus: false,
		enabled,
	});
};

export const useGetTaskNoteSpecificsList = (payload: TaskNoteSpecificsListParams, enabled = true) => {
	return useQuery({
		queryFn: () => getTaskNoteSpecificsList(payload),
		queryKey: [QueryKey.SpecificList, JSON.stringify(payload)],
		refetchOnWindowFocus: false,
		enabled,
	});
};

export const useUpdateHorseGeneralComment = () => {
	return useMutation({
		mutationFn: (payload: UpdateTaskCommentParams) => updateTaskComment(payload),
		mutationKey: [QueryKey.EditHorseGeneralComment],
	});
};

export const useReportTaskNoteExportCsvMutation = (
	options?: Omit<UseMutationOptions<Response<string> | undefined, Error, { payload: TaskNoteFilter }>, 'mutationFn'>
) => {
	return useMutation({
		mutationFn: ({ payload }) => exportReportTaskNoteCSV(payload),
		...options,
	});
};

export const useReportHorsesByOwnerCsvMutation = (
	options?: Omit<
		UseMutationOptions<Response<string> | undefined, Error, { payload: ReportHorseFinancePayload }>,
		'mutationFn'
	>
) => {
	return useMutation({
		mutationFn: ({ payload }) => exportHorsesByOwnerCsv(payload),
		...options,
	});
};

export const useReportHorsesByOwnerPdfMutation = (
	options?: Omit<
		UseMutationOptions<Response<string> | undefined, Error, { payload: ReportHorseFinancePayload }>,
		'mutationFn'
	>
) => {
	return useMutation({
		mutationFn: ({ payload }) => exportHorsesByOwnerPdf(payload),
		...options,
	});
};

export const useReportOwnersByHorsePdfMutation = (
	options?: Omit<
		UseMutationOptions<Response<string> | undefined, Error, { payload: ReportHorseFinancePayload }>,
		'mutationFn'
	>
) => {
	return useMutation({
		mutationFn: ({ payload }) => exportOwnersByHorsePdf(payload),
		...options,
	});
};

export const useExportPaddockByHorseTypeMutation = (
	options?: Omit<
		UseMutationOptions<Response<string> | undefined, Error, { payload: PaddockReportFilterPayload }>,
		'mutationFn'
	>
) => {
	return useMutation({
		mutationFn: ({ payload, type }: { payload: PaddockReportFilterPayload; type: string }) =>
			type === 'pdf' ? exportPaddockByHorseTypePdf(payload) : exportPaddockByHorseTypeCsv(payload),
		...options,
	});
};

export const useGetReportRaceResultsExportCsv = (
	options?: Omit<UseMutationOptions<Response<string> | undefined, Error, { payload: RaceResultsPayload }>, 'mutationFn'>
) => {
	return useMutation({
		mutationFn: ({ payload }) => getReportRaceResultsExportCsv(payload),
		...options,
	});
};

export const useExportPaddockBroodmaresMutation = (
	options?: Omit<
		UseMutationOptions<Response<string> | undefined, Error, { payload: PaddockReportFilterPayload }>,
		'mutationFn'
	>
) => {
	return useMutation({
		mutationFn: ({
			payload,
			type,
			paddockType,
		}: {
			payload: PaddockReportFilterPayload;
			type: string;
			paddockType: PaddockReportDataType;
		}) => {
			return type === 'pdf'
				? exportPaddockBroodmaresPdf(payload, paddockType)
				: exportPaddockBroodmaresCsv(payload, paddockType);
		},
		...options,
	});
};

export const useGetPaymentOwner = (payload: ReportPaymentOwnerPayload) => {
	return useQuery({
		queryFn: () => getReportPaymentOwner(payload),
		queryKey: [QueryKey.PaymentOwner, JSON.stringify(payload)],
		refetchOnWindowFocus: false,
	});
};

export const useExportPaddockByOwnerMutation = (
	options?: Omit<
		UseMutationOptions<Response<string> | undefined, Error, { payload: PaddockReportFilterPayload }>,
		'mutationFn'
	>
) => {
	return useMutation({
		mutationFn: ({ payload, type }: { payload: PaddockReportFilterPayload; type: string }) =>
			type === 'pdf' ? exportPaddockByOwnerPdf(payload) : exportPaddockByOwnerCsv(payload),
		...options,
	});
};

export const useExportCommunicationCsvMutation = (
	options?: Omit<
		UseMutationOptions<Response<string> | undefined, Error, { payload: CommunicationReportPayload }>,
		'mutationFn'
	>
) => {
	return useMutation({
		mutationFn: ({ payload, type }: { payload: CommunicationReportPayload; type: CommunicationReportLastUpdateType }) =>
			exportCommunicationCsv(type, payload),
		...options,
	});
};

export const useGetReportPaymentExportCsv = (
	options?: Omit<
		UseMutationOptions<Response<string> | undefined, Error, { payload: ReportPaymentOwnerPayload }>,
		'mutationFn'
	>
) => {
	return useMutation({
		mutationFn: ({ payload }) => getReportPaymentExportCsv(payload),
		...options,
	});
};

export const useExportCommunicationPdfMutation = (
	options?: Omit<
		UseMutationOptions<Response<string> | undefined, Error, { payload: CommunicationReportPayload }>,
		'mutationFn'
	>
) => {
	return useMutation({
		mutationFn: ({ payload, type }: { payload: CommunicationReportPayload; type: CommunicationReportLastUpdateType }) =>
			exportCommunicationPdf(type, payload),
		...options,
	});
};

export const useCommunicateOwnerResendEmailMutation = (
	options?: Omit<
		UseMutationOptions<Response<null> | undefined, Error, { payload: ReportCommunicationOwnerResendPayload }>,
		'mutationFn'
	>
) => {
	return useMutation({
		mutationFn: ({ payload }) => reportCommunicationOwnerResendEmail(payload),
		...options,
	});
};

export const useCommunicateHorseResendEmailMutation = (
	options?: Omit<
		UseMutationOptions<Response<null> | undefined, Error, { payload: ReportCommunicationHorseResendPayload }>,
		'mutationFn'
	>
) => {
	return useMutation({
		mutationFn: ({ payload }) => reportCommunicationHorseResendEmail(payload),
		...options,
	});
};

export const useGetStockCountBroodmaresStats = (payload: StockCountReportPayload) => {
	return useQuery({
		queryFn: () => getReportStockCountBroodmaresStats(payload),
		queryKey: [QueryKey.StockCountReportBroodmaresStats, JSON.stringify(payload)],
		refetchOnWindowFocus: false,
	});
};

export const useGetBroodmaresStatsExport = (
	options?: Omit<
		UseMutationOptions<Response<string> | undefined, Error, { exportType: string; params: StockCountReportPayload }>,
		'mutationFn'
	>
) => {
	return useMutation({
		mutationFn: ({ exportType, params }) => getBroodmaresStatsExport(exportType, params),
		...options,
	});
};

export const useGetStockCount = (payload: StockCountReportPayload) => {
	return useQuery({
		queryFn: () => getReportStockCount(payload),
		queryKey: [QueryKey.StockCountReport, JSON.stringify(payload)],
		refetchOnWindowFocus: false,
	});
};

export const useGetStockCountExport = (
	options?: Omit<
		UseMutationOptions<Response<string> | undefined, Error, { exportType: string; payload?: StockCountReportPayload }>,
		'mutationFn'
	>
) => {
	return useMutation({
		mutationFn: ({ exportType, payload }) => getStockCountExport(exportType, payload),
		...options,
	});
};
