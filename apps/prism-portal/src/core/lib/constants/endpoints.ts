export enum Endpoint {
	Login = '/login',
	ChangePassword = '/trainer/password',
	SSOLogin = '/sso/login',
	HorseProfile = '/horse/profile',
	HorseDuration = '/horse/duration',
	HorseFeed = '/horse_feed',
	GearManagement = '/gear/management',
	WorkRider = '/work-rider/list',
	UpdateWorkRider = '/work-rider',
	Logout = '/logout',
	DataConfig = '/data/config',
	ForgotPassword = '/public/password/recover',
	HorseList = '/horse/list',
	ResetPassword = '/public/password/reset',
	HorseListArchive = '/horse/list/archive',
	HorseListWishlist = '/horse/wishlist',
	TrainerList = '/trainer/list',
	HorseNameList = '/horse/search',
	SearchTrainer = '/public/search/trainer',
	SearchSireList = '/horse/search/sire',
	SearchDamList = '/horse/search/dam',
	UnitConfig = '/unitconfig',
	HorseOriginList = '/public/country',
	HorseBonusSchemeList = '/horse/bonusScheme',
	StaticData = '/static/data',
	HorseProfileFilter = '/horse/profile/filter',
	HorseListInfo = '/horse/list/info',
	PresetsList = '/slpreset',
	HorsePresetsList = '/horse/slpreset',
	HorseType = '/horse/type',
	HorseTypeSub = '/horse/type/sub',
	BreedingHorseList = '/breeding/horse/list',
	CreateHorse = '/horse/create',
	HorseListV2 = '/horse/list/v2',
	UpdateHorseProfile = '/horse/profile/update',
	Location = '/location',
	Status = '/horse/status',
	BoxList = '/barn/box/list',
	StatusList = '/horse/status/list',
	TreatmentFutureCheck = '/v2/treatment/future/check',
	Barn = '/barn',
	Box = '/barn/box/create',
	DeleteBox = '/barn/box/delete',
	UpdateBox = '/barn/box/update',
	HorseStud = '/horse/search/stud',
	Origin = '/horse/country/add',
	BarnBoxHistory = '/horse/barnbox_history',
	LocationHistory = '/horse/daysinwork',
	TrainerGrantorsList = '/trainer/grantors',
	DeleteLocationHistory = '/horse/daysinwork/delete',
	EditLocationHistory = '/horse/daysinwork/update',
	CreateLocationHistory = '/horse/daysinwork/create',
	HorseNoteChart = '/horse/note/v2/chart',
	HorseNoteBaseline = '/horse/note/v2/baseline',
	ArchiveHorse = '/horse/archive',
	UnArchiveHorse = '/horse/unarchive',
	RacingData = '/racing/horse',
	HorseGear = '/gear',
	CreateGear = '/trackwork/work/gear/create',
	KeepHorse = '/horse/keep',
	MergeAHorse = '/horse/merge',
	MergeHorse = '/horse/merge/horse',
	FeedSupplement = '/feed_supplement',
	HorseNoteList = '/horse/note/v2/list',
	HorseNote = '/horse/note/v2',
	SentEmailHorseNote = '/horse/note/v2/send',
	HorseConfig = '/horse/note/config',
	HorseDashboard = '/dashboard/horse',
	HorseMedia = '/horse/media',
	HorseMediaList = '/horse/media/list',
	HorseGeneral = '/v2/general/horse',
	GeneralTaskInfo = '/task/general/info',
	HorseGeneralCreateTask = '/task/general/create/multiple',
	HorseGeneralUpdateTask = '/task/general/update',
	HorseGeneralDeleteTask = '/task/general/delete',
	HorseGeneralComment = '/task/general/comment',
	HorseGeneralMarkAsCompletedTask = '/task/general/complete',
	Specific = '/general/specific',
	HorseReportBanner = '/stable/library/default',
	StableLibrary = '/stable/library',
	CreateHorseReportV1 = '/horse/report/new',
	StateData = '/public/state',
	RegionData = '/public/region',
	UpdateHorseReportV1 = '/horse/report/edit',
	DeleteHorseReport = '/horse/report/delete',
	HorseProfileMedia = '/horse/profile/media',
	HorsePedigree = '/horse/pedigree',
	ReportTemplate = '/report/template',
	ReportBodyTemplate = '/report/template/body',
	CreateHorseReportV2 = '/horse/report/v2/new',
	EditHorseReportV2 = '/horse/report/v2/edit',
	HorseMediaAlbumList = '/horse/media/album/list',
	HorseReportUpload = '/horse/report/upload',
	HorseMediaDetail = '/horse/media/detail',
	HorseBalances = '/horse/balances',
	HorseBalancesPDF = '/horse/balances/pdf',
	GetUnitConfig = 'unitconfig',
	HorseBalancesOwner = '/horse/balances/owner',
	PublicSearchHorse = '/public/search/horse',
	OwnerRequestHorse = '/owner/request/horse/v2',
	SendInvitationEmail = '/invitation/send',
	HorseAvatarUpdate = '/horse/avatar/update',
	ListTimezone = '/static/timezone',
	HorseProfileNote = '/horse/profile-note',
	ListInvoice = '/prism/invoice',
	VerifyEmail = '/public/email/verify',
	HorseTrackingLocation = '/finance/tracking/option/tracking_location',
	HorseMediaInfo = '/horse/media/info',
	HorseScanQR = '/horse/scan-qr',
	ExcludeBexHorseAvailabel = '/horse/bex/horse-available/exclude',

	//Share Horse
	ShareHorse = '/share/horse',
	ShareHorseReshare = '/share/horse/re-share', //Reshare Ownership & Location
	ShareHorseSearch = '/share/horse/search',
	ShareHorseRemove = '/share/horse/remove',
	SynchroniseTasks = '/share/task/type',
	ShareTaskSync = '/share/task/sync',
	ShareHorseApprove = '/share/horse/approve',
	ShareHorseTrainer = '/share/horse/trainer',
	ShareHorseReject = '/share/horse/reject',
	ShareOwnership = '/share/horse/share-ownership',
	HorseListPending = '/share/horse/pending',
	CheckShareHorse = '/share/horse/check',

	//user
	UpdateProfile = '/profile/update',

	// Racing
	RacesSchedule = '/races/schedule',
	RacesPdf = '/races/pdf',
	HarnessRace = '/harness/race',
	HarnessRacePermission = '/harness/race/permission',
	RacingCalendar = '/racing/calendar/list',
	RacingCalendarMetadata = '/racing/calendar/metadata',
	RacingCalendarMeet = '/racing/calendar/meet',
	RacePlan = '/horse/note/v2/race-plan',

	//Owner
	OwnerList = '/owner/list',
	OwnerProfile = '/owner/profile/display',
	OwnerLastupdate = '/v2/owner/lastupdate',
	OwnerStableupdate = '/v2/owner/stableupdate',
	SyndicateManagers = '/v2/syndicate/managers',
	SyndicateMembers = '/v2/syndicate/members',
	SyndicateMember = '/v2/syndicate/member',
	HorseTrackwork = '/v2/trackwork/horse',
	HorsesTrackwork = '/v2/trackwork/horse/multiple',
	EditWorkComment = '/task/trackwork/comment',
	WorkList = '/trackwork/work/list',
	DeleteWorkList = '/trackwork/work/delete',
	UpdateWorkList = '/trackwork/work/update',
	OrderWorkList = '/trackwork/work/order',
	SortWorkList = '/trackwork/work/sort',
	Assignees = '/v2/staff/assignees',
	SupplierList = '/supplier',
	WorkRiderDefault = '/work-rider/default',
	HorseWorkComment = '/horse/work-comment',
	GearUpdate = '/trainer/gearUpdate',
	TrackworkInfo = '/task/trackwork/info',
	CreateTrackwork = '/trackwork/work/create',
	UpdateTrackwork = '/task/trackwork/update',
	UpdateWorkLabel = '/task/trackwork/update/work-label',
	CreateMultipleTrackwork = '/task/trackwork/create/multiple',
	TaskCreateTrackwork = '/task/trackwork/create',
	TaskTrackworkDelete = '/task/trackwork/delete',
	FilterHorseDaysInWork = '/horse/daysinwork/filter',
	CheckLinkedTask = '/task/trackwork/checklinked',
	GetTrackworkPresets = '/task/trackwork/getFavourite',
	CreateTrackworkPreset = '/task/trackwork/favourite/create',
	DeleteTrackworkPreset = '/task/trackwork/deleteFavourite',
	RacesDetail = '/races/detail',
	RaceMeet = '/races/meet',
	RacesHorse = '/races/horse',
	HorseProcedure = '/v2/treatment/horse',
	EditProcedureComment = '/task/treatment/comment',
	DeleteProcedure = '/task/treatment/delete',
	ProcedureInfo = '/task/treatment/info',
	ProcedureReasonList = '/static/reason',
	ProcedureCategoriesList = '/task/treatment/categories',
	ProcedureSpecificsList = '/task/treatment/specifics',
	CreateProcedureSpecific = '/data/treatment/specific/add',
	EditProcedureSpecific = '/data/treatment/specific/edit',
	DeleteProcedureSpecific = '/data/treatment/specific/delete',
	OrderProcedureSpecific = '/data/treatment/specific/order',
	UpdateProcedureSpecific = '/task/treatment/specific/update',
	SortProcedureSpecific = '/data/treatment/specific/sort',
	CreateProcedure = '/task/treatment/create',
	EditProcedure = '/task/treatment/update',
	MarkCompletedProcedure = '/task/treatment/complete',
	ProcedurePresetsList = '/task/treatment/favorites',
	CreateProcedurePreset = '/task/treatment/favorites/add',
	DeleteProcedurePreset = '/task/treatment/favorites/delete',
	TreatmentVaccine = '/v2/treatment/vaccine',
	FinanceConfigProcedureSearch = '/finance/config/procedure/search',
	FinanceConfigProcedure = '/finance/config/procedure',
	StallionCategory = '/stallion/category',
	OwnerCategory = '/owner/category',
	OwnerHorseCategory = '/owner/category/horse',
	OwnerSendEmail = '/owner/send',
	ExportOwner = '/owner/report/export',
	StaffAdvisors = '/staff/advisors',
	HorseReportList = '/horse/report/list',
	HorseReportDetail = '/horse/report/preview',
	HorseReportSendEmailV2 = '/horse/report/send',
	HorseReportDetailV2 = '/horse/report/v2/preview',
	PreviewHorseReportDetailV2 = '/horse/report/v2/preview/request',
	HorseTransport = '/task/transport/list',
	EditTransportComment = '/task/transport/comment',
	DeleteTransport = '/task/transport/delete',
	TransportInfo = '/task/transport/info',
	CheckCompleteTransport = '/task/transport/check',
	MarkCompletedTransport = '/task/transport/complete',
	MarkCompletedAllTransports = '/task/transport/completeAll',
	VehicleList = '/v2/vehicle',
	SearchTransportConfig = '/finance/config/transport/search',
	UpdateHorseLocation = '/horse/location/update',
	CreateTransport = '/task/transport/create',
	EditTransport = '/task/transport/update',
	TransportConfig = '/finance/config/transport',
	OwnerGroup = '/v2/owner/group',
	InfusionsoftRanking = '/infusionsoft/ranking',
	Owner = '/v2/owner',
	CheckOwnerEmail = '/owner/profile/email/check',
	InfusionConfigData = '/owner/infusion-config-data',
	SupportContact = '/support/contact',
	SyncFinanceAccount = '/v2/owner/sync_finance_account',
	OwnerRemove = '/v2/owner/remove',
	OwnerResent = '/v2/owner/reinvite/',
	SuspendOwner = '/owner/suspend',
	ExportOwnershipChange = '/export/ownership_changes',
	OwnerSilkColor = '/v2/owner/silk_color',
	OwnerNote = '/v2/owner/note',
	FinanceOwnerAccount = '/finance/owner/account',
	OwnerCharity = '/finance/owner/account/charity/',
	OwnerSendRequest = '/finance/importInvoice/trainer/request',
	FinanceExternalId = '/finance/owner/external-id',
	TransportLocation = '/task/transport/location',
	TransportCopy = '/task/transport/copy',
	OwnerAccountHorse = '/finance/owner/account/horse',
	UpdateOwnerAccountHorse = '/finance/owner/account/horse/discount-byhorse',
	TransportDeleteBatch = '/task/transport/delete/batch',
	GeneralDeleteBatch = '/task/general/delete/batch',
	StaffTrackwork = '/v2/trackwork/staff',
	StaffProcedure = '/v2/treatment/staff',
	StaffGeneral = '/v2/general/staff',
	TrainerTrackwork = '/v2/trackwork/trainer',
	TrainerProcedure = '/v2/treatment/trainer',
	TrainerGeneral = '/v2/general/trainer',
	OwnerKeapNote = '/owner/note',
	OwnerGeneral = '/task/general',
	OwnerCommunications = '/infusionsoft/emaillogs',
	OwnerReportFilterList = '/owner/report-filter',
	RequestPrismId = '/finance/importInvoice/trainer',

	// Finance
	CompareBalance = '/finance/export/compare-balance',
	DebtorsReport = '/finance/balances',
	DebtorsReportOwnerAccount = '/finance/balances/owneraccount',
	DebtRecovery = '/debt-recovery',
	DirectDebit = '/finance/directdebit',
	OverViewStatement = '/finance/statement/overdue',
	Statements = '/finance/statement',
	FilterDebtRecovery = '/debt-recovery/filter',
	FilterStatement = '/finance/statement/filters',
	ManagementFee = '/finance/config/management-fee/horse',
	AccountFinanceSettings = '/account/settings/finance',
	DownloadDebtRecovery = '/debt-recovery/download_letter',
	ExportFileDebtRecovery = '/finance/export/debt/progress',
	FinanceConfigFarrier = '/finance/config/farrier',
	FinanceConfigDentist = '/finance/config/dentist',
	FinanceConfigFarrierSearch = '/finance/config/farrier/search',
	FinanceConfigDentistSearch = '/finance/config/dentist/search',
	SendToLegal = '/debt-recovery/send-to-legal',
	RuleConfig = '/finance/config',
	RuleConfigDayrates = '/finance/config/dayrates',
	RuleConfigImport = '/finance/config/import',
	DirectDebitBatchPayments = '/finance/directdebit/batchpayments',
	TransactionFinance = '/finance/expense/list',
	FinanceAccount = '/finance/account',
	FinanceTrackingCategory = '/finance/tracking/category',
	FinanceTrackingoption = '/finance/tracking/option',
	FinanceTrackingCategoryLocation = '/finance/tracking/category/location',
	FinanceTrackingoptionRestore = '/finance/tracking/option/restore',
	FinanceSpecific = '/specific',
	FinanceTaxRate = '/finance/taxrate',
	FinanceExpenseUpdateConfig = '/finance/expense/update/config',
	FinanceExpenseItem = '/finance/expense/item/created',
	FilterTransaction = '/finance/expense/filters',
	FinancePeriod = '/finance/period',
	FinanceExport = '/finance/export/zip',
	DebtorsReportPdf = '/finance/balances/pdf',
	DebtorsReportCsv = '/finance/balances/export',
	ExportTransaction = '/finance/expense/export',
	ExportCsvStatement = '/finance/statement/export',
	ExportProgressStatement = '/finance/export/statements/progess',
	DownloadStatement = '/finance/export/statements',
	DownloadZipStatement = '/finance/export/statements/zip',
	ImportInvoiceBy = '/finance/importInvoice/invoiceBy',
	FinanceItemList = '/finance/expense/item',
	FinanceMethodList = '/finance/config/method',
	FinanceTaxType = '/finance/taxType',
	OwnerFinanceList = '/finance/owner',
	FinanceChartOfAccounts = '/finance/account/chart',
	ApplyManagementFee = '/finance/config/management-fee/apply',
	CustomManagementFee = '/finance/config/management-fee/custom',
	FinanceProgress = '/finance/progress',
	XeroOrganization = '/xero/organization',
	XeroDisconnect = '/xero/disconnect',
	XeroSync = '/xero/sync',
	XeroCheck = '/xero/sync/check',
	XeroTokenCheck = '/xero/token/check',
	DateRangeValidation = '/finance/daterange/validation',
	TaskCount = '/finance/task/count',
	FinanceTask = '/finance/task',
	FinanceTaskHorse = '/finance/task/horse',
	FinanceTaskAssignee = '/finance/task/assignee',
	FinanceTaskExport = '/finance/task/export',
	FinanceManualDayRateExpense = '/finance/expense/manual-day-rate',
	FinanceSyndicateFeeExpense = '/finance/expense/syndicate-fee',
	FinanceAccountType = '/finance/account/type',
	RefreshManagementFee = '/finance/expense/management-fee/regenerate',
	RefreshStatement = '/finance/statement/pdf',
	FinanceStatementPreview = '/finance/statement/preview/html',
	FinanceStatementAttachment = '/finance/statement/attachments',
	FinanceExpenseAttachment = '/finance/expense/attachment',
	FinanceOrganisation = '/finance/setting/organisation',
	TrainerContact = '/trainer/profile/contact',
	SendEmailStatement = '/finance/statement/send/email',
	AllStatement = '/finance/statement/review/all',
	ExportStatementLocation = '/finance/monthend/check/locations/export',
	ExportStatementDailyCharge = '/finance/monthend/check/configs/export',
	StatementLocation = '/finance/monthend/check/locations',
	StatementNoDailyCharge = '/finance/monthend/check/configs',
	FinanceFindHorse = '/finance/expense/find/horse',
	FinanceFindHorseExport = '/finance/expense/find/horse/export',
	FinanceExpense = '/finance/expense',
	FinanceTempExpense = '/finance/tempExpense',
	OwnershipExpense = '/v2/ownership/list',
	SplitExpense = '/finance/expense/split',
	StatementConfig = '/finance/setting/statement_config',
	PaymentSetting = '/finance/setting/payment',
	PaymentSettingBank = '/finance/bank/setting',
	CreditExpense = '/finance/expense/credit',
	StatementCodeFormat = '/finance/setting/statement_code_format',
	AddGroupExpense = '/finance/expense/group',
	DeleteGroupExpense = '/finance/expense/group/delete',
	ManagementFeeExpense = '/finance/expense/management-fee',
	HorseOfFinanceOwnerAccount = '/v2/ownership/list/owner',
	ExportDirectDebitPendingList = '/finance/directdebit/csv/export/not-setup-list',
	ImportDirectDebitSetupFile = '/finance/directdebit/csv/import',
	SearchHorse = '/finance/horse/search',
	GroupExpensesByLocationStatus = '/finance/expense/group/location',
	GroupExpensesByRace = '/finance/expense/group/byrace',
	GroupExpensesArrival = '/finance/expense/group/arrival',
	GroupExpensesDeparture = '/finance/expense/group/departure',
	SendDirectDebit = '/finance/owner/debit_request',
	DirectDebitUnregisteredOwner = '/finance/directdebit/unregister-owner',
	FinanceExportPDF = '/finance/monthend/export/pdf',
	UpdateStatusExpense = '/finance/expense/status',
	ApprovedExpense = '/finance/expense/approve',
	StatementHorseNote = '/finance/statement/note/horse',
	FinanceExpenseHorseGroup = '/finance/expense/list/group',
	PreviewOrganizationConfig = '/finance/statement/preview/sample',
	FinanceDebitLink = '/finance/owner/debit_request/link',
	ManagementFeeOwner = '/finance/config/management-fee/owner',
	DayRateDiscount = '/finance/owner/account/dayrates/discount',
	FinanceExpenseUpdateMultiple = '/finance/expense/update/multiple',
	HorseSalePreset = '/finance/statement/horsesalepreset',
	FinanceStatementHorseSale = '/finance/statement/horsesale',
	FinanceHorseSalePreset = '/finance/horse-sale/preset',
	FinanceStatementSingle = '/finance/statement/single',
	RegenerateStatement = '/finance/statement/regen',
	RegenerateHorseStatement = '/finance/statement/regen/horse',
	RegenerateStatementExpenses = '/finance/statement/regen/expenses',
	RegenerateStatementExpensesList = '/finance/statement/regen/expenses/list',
	SplitRegenerateStatementExpense = '/finance/statement/regen/expenses/split',
	UpdateRegenerateStatementExpense = '/finance/statement/regen/expenses/update',
	ApproveRegenerateStatementExpense = '/finance/statement/regen/expenses/approve',
	GetRegenerateStatementExpenseStatus = '/finance/statement/regen/expenses/status',
	GetRegeneratedStatements = '/finance/statement/regen/statement',
	CreateRegenerateStatementExpense = '/finance/statement/regen/expenses/create',
	DeleteRegenerateStatementExpense = '/finance/statement/regen/expenses/delete',
	CreateRegenerateStatementCredit = '/finance/statement/regen/credit/create',
	UpdateRegenerateStatementCredit = '/finance/statement/regen/credit/update',
	DeleteRegenerateStatementCredit = '/finance/statement/regen/credit/delete',
	CreateRegenerateStatementManagementFee = '/finance/statement/regen/management-fee/create',
	UpdateRegenerateStatementManagementFee = '/finance/statement/regen/management-fee/update',
	RegenerateStatementDailyCharge = '/finance/statement/regen/manual-day-rate',
	FinanceStatementDiscount = '/finance/statement/discount',
	AdjustExpenseDiscount = '/finance/expense/detail/discount',
	RegenerateStatementPayment = '/finance/statement/regen/payment',
	FinanceExpenseCount = '/finance/expense/count',
	FinanceStatementDraft = '/finance/statement/draft',
	FinanceStatementDraftProgress = '/finance/statement/draft/progress',
	FinanceStatementDraftOwnerAccount = '/finance/statement/draft/owner_account',
	FinanceCheckDiscount = '/finance/monthend/check/discount',
	FinanceDraftDiscount = '/finance/statement/draft/discount',
	FinanceDraftCreditNote = '/finance/statement/draft/creditnote',
	FinanceInterest = '/finance/monthend/interest',
	ImportExpenseFile = '/finance/expense/group/export',
	CheckNSWInvoice = '/finance/sps/pdf',
	ImportNSWInvoice = '/finance/expense/import-expense',
	ExportStatementDraft = '/finance/statement/draft/export',
	ExportStatementDraftStats = '/finance/statement/draft/export/stats',
	CheckStatementExportProgress = '/finance/statement/export/progress',
	CancelStatementExport = '/finance/statement/export/cancel',
	DraftExportCSV = '/finance/statement/draft/export/csv',
	FinanceMonthendStatement = '/finance/monthend/statement',
	FinanceStatementReview = '/finance/statement/review',
	FinanceMonthendProgress = '/finance/monthend/progress',
	StatementReviewExport = '/finance/statement/review/export',
	RevertStatement = '/finance/monthend/statement/revert',
	RegenerateStatementOwnerAccounts = '/finance/statement/regen/ownerAccount',
	FinanceStatementOutstanding = '/finance/statement/outstanding',
	FinancePaymentStatus = '/finance/statement/payment_status',
	ExpenseGenerate = '/finance/expense/generate/data',
	FinanceRequestPay = '/finance/requestpay/submit',
	FinanceSendReminder = '/finance/overdue/reminder/send',
	CloseDebtRecovery = '/debt-recovery/close',
	DebtRecoveryPaymentPlan = '/debt-recovery/paymentplan',
	DebtRecoverySendEmail = '/debt-recovery/send-recovery-email',
	ExpenseOwner = '/finance/expense/owner',
	ExpenseOwnerFilter = '/finance/expense/owner/filter',
	ExpenseOwnerSum = '/finance/expense/owner/sum',
	FinanceStatementImportInvoice = '/finance/importInvoice',
	StatementPaymentRevert = '/statement/payment/revert',
	SmartSheet = '/smart/sheet',
	ManualPayment = '/statement/payment',
	PaymentConfirm = '/statement/payment/confirm',
	PaymentLog = '/statement/payment/log',
	CheckLastImport = '/finance/openingbalance/check/last-imported',
	PaymentBatch = '/statement/payment/batch',
	StatementPaymentInvoice = '/statement/payment/invoice',
	StatementPaymentAccount = '/statement/payment/account',
	PaymentDebtrecovery = '/payment/debtrecovery',
	ImportOpeningBalance = '/finance/openingbalance/import',
	CheckRedisInprogress = '/finance/redis/in-progress',
	OpeningBalanceOwner = '/finance/openingbalance/owners',
	OpeningBalanceSubmit = '/finance/openingbalance/submit',
	OpeningBalanceMap = '/finance/openingbalance/owner/map',
	RedisProgress = '/finance/redis/progress',
	OpeningBalancePermission = '/finance/openingbalance/permission',
	OpeningBalanceFinish = '/finance/openingbalance/finish',
	OpeningBalanceCrawl = '/crawl/opening/contacts',
	OpeningBalanceCrawlSubmit = '/crawl/opening/invoices',
	OpeningBalanceCrawlLog = '/crawl/opening/log',
	PaymentDirectdebit = '/payment/directdebit',
	PaymentDirectdebitCustomer = '/payment/directdebit/customer',
	DirectdebitExportCsv = '/finance/directdebit/export',
	BannerHeaderFooter = '/account/banners',

	// Staff
	StaffList = '/staff/list',
	StaffProfile = '/staff/profile/display',
	StaffPosition = '/data/position',
	CreatePosition = '/data/position/add',
	StaffPermission = '/v2/permission',
	StaticFeature = '/static/feature',
	CreateStaff = '/staff/create',
	UpdateStaff = '/staff/profile/update',
	StaffTaskPending = '/task/pending',
	StaffDeactivate = '/staff/deactive',
	StaffReactivate = '/staff/reactive',
	StaffDelete = '/staff/delete',
	StaffReinvite = '/staff/reinvite',

	// Ownership
	Ownership = '/v2/ownership',
	OwnershipTransaction = '/v2/ownership/transaction',
	OwnershipCount = '/v2/ownership/count',
	OwnershipTransactionLater = '/v2/ownership/transaction/later',
	OwnershipTransactionExpense = '/v2/ownership/transaction/expense',
	SyndicateGroup = '/v2/syndicate',
	OwnershipPermission = '/v2/ownership/permission',
	OwnerAdviceSetting = '/owner_advice/setting',
	CheckOwnerAdviceConfirmation = '/owner_advice/multiple/confirm',
	CheckOwnerAdviceConfirm = '/owner_advice/confirm',
	CheckOwnerAdviceMultipleNotify = '/owner_advice/multiple/notify',
	CheckOwnerAdviceNotify = '/owner_advice/notify',
	AdviceEmailContent = '/owner_advice/email_content',
	AdviceEmailContentMultiple = '/owner_advice/multiple/email_content',
	AdviceTask = '/owner_advice/task',
	AdvicePreviewAttachment = '/owner_advice/notify/attachment',

	// Comms
	CommsGroupUpdateList = '/horse/report/group/list',
	CommsStableUpdate = '/v2/general/update',
	CommsGroupUpdatePreview = '/horse/report/group/preview',
	CommsStableUpdatePreview = '/v2/general/update/preview',
	DeleteCommsGroupUpdate = '/horse/report/group/delete',
	GroupUpdateSendEmail = '/horse/report/group/send',
	StableUpdateSendEmail = '/v2/general/update/send',
	StableUpdateSender = '/v2/general/update/sender',
	CommsReportGroupUpdate = '/horse/report/group',
	SyndicateUpdateV2 = '/v2/general/update/v2',
	PreviewSyndicateUpdateV2 = '/v2/general/update/preview/request',
	CreditPage = '/credit/credit-page',

	// Syndicator
	Syndicator = '/syndicator',
	SyndicatorProfile = '/syndicator/profile',

	// Trainer
	TrainerProfile = '/trainer/profile/display',
	UpdateOrganizationConfig = '/trainer/profile/finance/update',
	GetOrganizationConfig = '/v2/trainer',

	// Plus
	CommunicationReport = '/communication/report',
	LastUpdate = '/communication/report/last-update',
	CommunicationReportType = '/communication/report/type',
	Quicksight = '/quicksight',

	// Media
	MediaUpload = '/media/upload',
	Upload = '/upload/v2',
	ChunksDone = '/upload/v2/chunksdone',

	//Supplier
	CheckExistExpenseOrRule = '/supplier/check-exist-expense-or-rule',
	UnArchiveSupplier = '/supplier/un-archive',

	// Dashboard
	TaskReportList = '/v2/report',
	InfusionsoftDashboard = '/infusionsoft/horse/remainingshare',
	InfusionsoftSync = '/infusionsoft/sync',
	InfusionsoftProgress = '/infusionsoft/progress',
	InfusionsoftToken = '/infusionsoft/token',
	InfusionsoftOpportunity = '/infusionsoft/opportunity',
	InfusionsoftStage = '/infusionsoft/stage',
	Reminder = '/reminder/list',
	ReminderUpdate = '/reminder/update',
	ReminderCreate = '/reminder/create',
	ReminderDelete = '/reminder/delete',

	// Schedule
	Calendar = '/v2/calender',
	Trackwork = '/v2/trackwork',
	DeleteTrackworkGroup = '/data/trackwork/group/delete',
	OrderTrackworkGroup = '/data/trackwork/group/order',
	UpdateTrackworkGroup = '/data/trackwork/group/edit',
	DataTrackworkGroupList = '/data/trackwork/group/list',
	DataTrackworkGroupAdd = '/data/trackwork/group/add',
	TaskTrackworkGroup = '/task/trackwork/group',
	TaskTrackworkGroupOrder = '/task/trackwork/group/order',
	TaskTrackworkEtrakkaDetail = '/task/trackwork/etrakka/detail',
	TaskTrackworkArioneoDetail = '/task/trackwork/arioneo/detail',
	TaskTrackworkArioneoConfig = '/task/trackwork/arioneo/config',
	TaskTrackworkArioneoSearch = '/task/trackwork/arioneo/search',
	TaskTrackworkEtrakkaSearch = '/task/trackwork/etrakka/search',
	TaskTrackworkArioneoArchived = '/task/trackwork/arioneo/archived',
	TaskTrackworkEtrakkaArchived = '/task/trackwork/etrakka/archived',
	TaskTrackworkEtrakkaHide = '/task/trackwork/etrakka/hide',
	ConnectArioneoAccount = '/task/trackwork/arioneo/account/link',
	TaskTrackworkLink = '/task/trackwork/link',
	TaskTrackworkUnlinkMultiple = '/task/trackwork/unlink/multiple',
	UnlinkTrackwork = '/task/trackwork/unlink',
	TrackworkVersion = '/v2/trackwork/versioning',
	TrackworkViewPdf = '/v2/trackwork/view/pdf',
	TaskTrackworkCopy = '/task/trackwork/copy',
	TaskTrackworkCopyProgress = '/task/trackwork/copy/progress',
	TaskGeneralCopy = '/task/general/copy',
	EquestrianTack = '/equestrian/tack',
	EquestrianTackList = '/equestrian/tack/list',
	EquestrianTackHorse = '/equestrian/tack/horse',
	EquestrianTackDefault = '/equestrian/tack/default',
	EquestrianCompetitionTack = '/equestrian/competition/tack',
	ScheduleHorseFeed = '/horse_feed/schedule',
	ScheduleHorseFeedComment = '/horse_feed/comment',
	ScheduleHorseFeedDownload = '/horse_feed/tally',
	TaskTrackworkArioneoHide = '/task/trackwork/arioneo/hide',

	//Horse sale
	HorseSale = '/v2/horse/available',
	HorseSaleOrder = '/v2/horse/available/order',
	Config = '/web/config',
	SearchStable = '/audit/search/stable',
	HorseCategory = '/web/horse/category',
	HorseGender = '/v2/horse/available/gender',
	LocationSearch = '/location/search',
	PlaceDetail = '/location/place-detail',
	PushToBex = '/bex/horse/publish',
	UnPublishToBex = '/bex/horse/unpublish',
	BexVendor = '/bex/vendor',

	// Lifetime View
	LifetimeViewHorseList = '/life-time-view/horse',
	TaskHorse = '/task/horse',
	HorseNotes = '/horse/note/v2/lifetime',
	LifetimeViewExportCsv = '/horse/lifetime/export',
	RacePlanningExportCsv = '/races/planning/export',
	WeekScheduleExportPdf = '/horse/note/v2/week-planning/pdf',

	//Schedule
	TreatmentSchedule = '/v2/treatment/schedule',
	ScheduleNoteListDate = '/schedule/note/list/date',
	ScheduleNoteDelete = '/schedule/note/delete',
	ScheduleNoteCreate = '/schedule/note/create',
	ScheduleNoteEdit = '/schedule/note/edit',
	TaskTreatmentCopy = '/task/treatment/copy',
	TaskTrackworkGroupEdit = '/task/trackwork/group/edit',
	FarrierWorksheet = '/farrier-worksheet',
	FarrierWorksheetFilter = '/farrier-worksheet/filter',
	FarrierWorksheetExportCSV = '/farrier-worksheet/export/csv',
	FarrierWorksheetStats = '/farrier-worksheet/stats',
	WorksheetPrint = '/farrier-worksheet/export/pdf',

	// Account
	AccountSettings = '/account/settings',
	AccountSettingsPrintBrandsCheck = '/account/settings/print/brands/check',

	ScheduleGeneral = '/v2/general/schedule',

	//Report
	HorseReport = '/horse/bonusscheme',
	EmailLogReport = '/v2/report/mail_log',
	EmailResend = '/v2/report/mail/resend',
	EmailReactivate = '/v2/report/mail/reactive-email',
	EmailLogExportCsv = '/v2/report/mail_log/export',
	ForwardEmailLog = '/v2/report/mail/forward',
	EmailLogSearchTrainer = '/audit/search/trainer',
	LocationStatus = '/v2/report/location_status',
	HorseNoteReminder = '/horse/note/reminder',
	GearList = '/trackwork/work/gear/list',
	ReportTask = '/v2/report/tasks',
	ReportSpecificsList = '/v2/treatment/specifics',
	ReportExport = '/v2/report/export',
	FinanceHorseReport = '/finance/horse/report',
	ReportHorseByOwner = '/breeding/report/horsebyowner',
	DailyReport = '/horse/daily/report',
	DailyReportExport = '/horse/daily/report/export',
	DailyReportPdf = '/horse/daily/report/pdf',
	SpecificReport = '/horse/specific/report',
	SpecificReportExport = '/horse/specific/report/export',
	SpecificReportPdf = '/horse/specific/report/pdf',
	ReportPaddock = '/breeding/report/paddock',
	ReportCommunicationHorse = '/lastupdate/horse',
	ReportCommunicationOwner = '/lastupdate/owner',
	ReportRaceResults = '/races/statistic/weekly',
	ReportStockCountBroodmaresStats = '/broodmare-stats',
	ReportStockCount = '/breeding/stock-count',

	// Insurance
	Insurance = '/insurance',
	InsuranceProvider = '/insurance/insurer',
	InsuranceClient = '/insurance/client',
	InsuranceDraft = '/insurance/draft',
	InsuranceSurvey = '/insurance/survey',
	RefreshOwnershipList = '/insurance/owner/refresh',
	InsuranceExtra = '/insurance/extra',
	InsurancePolicyListing = '/insurance/report/policy/listing',
	InsurancePolicyListingFilter = '/insurance/report/policy/listing/filter',
	InsuranceDetail = '/insurance/detail',
	InsuranceUninsuredExport = '/insurance/report/uninsured/csv',
	InsuranceUninsured = '/insurance/report/uninsured',
	InsuranceHorseListingExport = '/insurance/report/horse/listing/csv',
	InsurancePolicyListingExport = '/insurance/report/policy/listing/csv',
	InsuranceUninsuredFilter = '/insurance/report/uninsured/filter',
	InsuranceHorseSearch = '/insurance/search',
	InsuranceTemplate = '/insurance/template',
	InsuranceHorseListing = '/insurance/report/horse/listing',
	InsuranceManual = '/insurance/manual',
	InsuranceDocuments = '/insurance/documents',
	InsuranceQuestions = '/insurance/question',
	InsuranceSendPolicy = '/insurance/owner/send',
	CreateInsuranceExpense = '/finance/insurance/expense',
	CreateInsuranceInvoice = '/finance/insurance/invoice',
	InsuranceInfo = 'insurance/survey/info',

	//My task
	MytaskTrackwork = '/v2/trackwork/mytask',
	MytaskTreatment = '/v2/treatment/mytask',
	MytaskGeneral = '/v2/general/mytask',

	//notifications
	NotificationMe = '/notification/me',
	MarkNotificationAsRead = '/notification/markAsRead',
	NotificationSettings = '/notification/settings',
	AccountSetting = '/account/setting/load',
	UpdateNotificationSetting = '/account/setting/update',

	//cms
	News = '/audit/search/news',
	UpdateNews = '/audit/edit/news',
	CreateNews = '/audit/create/news',
	DeleteNews = '/audit/delete/news',
	BannerVideo = '/web/banner',
	UploadUrl = '/media/upload/link',
	PhotoGallery = '/web/media',
	Staff = '/web/staff',
	StaffOrder = '/web/staff/order',
	BannerImage = '/website/banner',
	OrderBannerImage = '/website/banner/order',
	CmsCustomField = '/web/stable_custom_field',
	WebConfiguration = '/web/configuration',
	Event = '/cmr/web/event',
	WebStats = '/web/stats',
	WebStatsList = '/web/stats/list',
	WebStatsOrder = '/web/stats/order',
	Testimonial = '/web/testimonial',
	Job = '/cmr/web/job',
	WebOwnerShip = '/web/ownership',
	CmsRaceMedia = '/web/race/latest_winners',
	UpdateEntryMedia = '/web/race/update_entry_media',

	//Bulk update
	Rollover = '/rollover',
	RolloverConfig = '/rollover/config',
	RolloverFilter = '/rollover/filter',

	// PRA Form
	HorsePRASearch = '/horse/PRA/search',
	ExportPRARegistrationForm = '/export/pra/registration',
	SendEmailPRAForm = '/export/pra/registration/sent',
	ExportChangeOfOwnerForm = '/export/pra/transfer_os',
}
